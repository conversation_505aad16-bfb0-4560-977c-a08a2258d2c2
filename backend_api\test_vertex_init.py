#!/usr/bin/env python3
"""
Script para probar la inicialización de Vertex AI.
"""

import sys
sys.path.append('app')

from app.services.vertex_ai import initialize_gemini_model
from app.tools.tradingview_provider import get_available_tools

def test_vertex_initialization():
    """Probar la inicialización de Vertex AI."""
    print("🧪 === PRUEBA DE INICIALIZACIÓN VERTEX AI ===")
    
    try:
        # Obtener herramientas disponibles
        print("1. Obteniendo herramientas disponibles...")
        tools = get_available_tools()
        print(f"   ✅ {len(tools)} herramientas disponibles")
        
        # Intentar inicializar Gemini
        print("2. Inicializando modelo Gemini...")
        model = initialize_gemini_model(tools)
        
        if model:
            print("   ✅ Modelo Gemini inicializado correctamente")
            print(f"   📋 Tipo de modelo: {type(model)}")
            return True
        else:
            print("   ❌ Modelo Gemini NO se inicializó")
            return False
            
    except Exception as e:
        print(f"   ❌ Error durante la inicialización: {e}")
        import traceback
        print(f"   🔍 Traceback completo:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_vertex_initialization()
    if success:
        print("\n🎉 ¡Inicialización exitosa!")
    else:
        print("\n💥 Inicialización falló")
    
    sys.exit(0 if success else 1)

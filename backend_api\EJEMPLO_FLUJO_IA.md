# 🤖 Cómo se Envía Información a la IA - TradingIA

## 📡 1. Request HTTP desde Frontend

### Estructura del Request
```typescript
// Frontend (React/TypeScript)
const request: ChatRequest = {
  message: "¿<PERSON><PERSON><PERSON>l es el precio de Tesla?",
  history: [
    {
      role: "user", 
      content: "Hola",
      timestamp: "2025-08-15T10:00:00Z"
    },
    {
      role: "assistant",
      content: "¡Hola! ¿En qué puedo ayudarte?",
      timestamp: "2025-08-15T10:00:05Z"
    }
  ]
}

// Envío con autenticación
const response = await fetch('/api/v1/chat/', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(request)
})
```

## 🛡️ 2. Validación de Autenticación

```python
# backend_api/app/routes/chat.py
async def get_current_user(credentials: HTTPAuthorizationCredentials):
    # Extrae el JWT token del header Authorization
    token = credentials.credentials
    
    # Valida con Supabase
    user_info = await validate_user_token(token)
    
    return user_info  # {"id": "user123", "email": "<EMAIL>"}
```

## 🤖 3. Inicialización del Modelo Gemini

```python
# backend_api/app/services/vertex_ai.py
def initialize_gemini_model(tools: List[Any]):
    # Configurar herramientas financieras
    vertex_tools = []
    for tool in tools:
        vertex_tools.append({
            "function_declarations": [{
                "name": tool["name"],
                "description": tool["description"], 
                "parameters": tool["parameters"]
            }]
        })
    
    # Crear modelo con instrucciones del sistema
    model = GenerativeModel(
        model_name="gemini-2.5-pro",
        tools=vertex_tools,
        system_instruction="""
        Eres un asistente financiero especializado.
        
        CUANDO el usuario pregunte por PRECIOS:
        - USA get_price_data
        
        CUANDO pida INDICADORES:
        - USA apply_indicator
        
        Responde SIEMPRE en español.
        """
    )
    
    return model
```

## 📝 4. Preparación del Historial de Conversación

```python
# backend_api/app/routes/chat.py
async def _process_chat_request(request: ChatRequest, current_user):
    # Convertir historial a formato Vertex AI
    conversation_messages = []
    
    # Agregar mensajes del historial
    for msg in request.history:
        conversation_messages.append({
            "role": msg.role,      # "user" o "assistant"
            "content": msg.content # "¿Cuál es el precio de Tesla?"
        })
    
    # Agregar mensaje actual
    conversation_messages.append({
        "role": "user",
        "content": request.message
    })
    
    return conversation_messages
```

## 🚀 5. Envío a Vertex AI (Gemini)

```python
# backend_api/app/services/vertex_ai.py
async def generate_chat_response(messages: List[Dict], model):
    # Convertir mensajes a formato Content de Vertex AI
    chat_history = []
    
    for i, msg in enumerate(messages[:-1]):  # Todos menos el último
        if msg["role"] == "user":
            chat_history.append(Content(
                role="user",
                parts=[Part.from_text(msg["content"])]
            ))
        elif msg["role"] == "assistant":
            chat_history.append(Content(
                role="model", 
                parts=[Part.from_text(msg["content"])]
            ))
    
    # Iniciar chat con historial
    chat = model.start_chat(history=chat_history)
    
    # Enviar mensaje actual
    current_prompt = messages[-1]["content"]
    response = chat.send_message(current_prompt)
    
    return response
```

## 🎯 6. Procesamiento de Function Calls

```python
# Si Gemini decide usar herramientas
if response.candidates[0].content.parts[0].function_call:
    function_call = response.candidates[0].content.parts[0].function_call
    
    # Extraer información de la función
    function_name = function_call.name
    function_args = dict(function_call.args)
    
    # Ejecutar la función correspondiente
    if function_name == "get_price_data":
        result = get_price_data(
            symbol=function_args["symbol"],
            interval=function_args["interval"], 
            n_bars=function_args["n_bars"]
        )
    elif function_name == "apply_indicator":
        result = apply_indicator(
            symbol=function_args["symbol"],
            interval=function_args["interval"],
            indicator_name=function_args["indicator_name"],
            parameters=function_args["parameters"]
        )
    
    # Enviar resultado de vuelta a Gemini
    function_response = chat.send_message(
        Content(parts=[Part.from_function_response(
            name=function_name,
            response={"result": result}
        )])
    )
```

## 📊 7. Obtención de Datos Financieros

```python
# backend_api/app/tools/tradingview_provider.py
def get_price_data(symbol: str, interval: str, n_bars: int):
    # Conectar con TradingView
    tv = TvDatafeed()
    
    # Parsear símbolo
    exchange, ticker = symbol.split(":")
    
    # Obtener datos
    data = tv.get_hist(
        symbol=ticker,
        exchange=exchange,
        interval=INTERVAL_MAP[interval],
        n_bars=n_bars
    )
    
    # Formatear para la IA
    formatted_data = {
        "symbol": symbol,
        "interval": interval,
        "bars_count": len(data),
        "data": [
            {
                "datetime": "2025-08-14T15:30:00",
                "open": 335.76,
                "high": 340.47,
                "low": 330.40,
                "close": 335.58,
                "volume": 75000662
            }
        ],
        "latest_price": 335.58
    }
    
    return formatted_data
```

## 💾 8. Guardado en Base de Datos

```python
# backend_api/app/services/supabase_client.py
async def save_chat_history(user_id: str, messages: List, ai_response: str):
    # Guardar en Supabase
    chat_data = {
        "user_id": user_id,
        "request_messages": messages,
        "ai_response": ai_response,
        "conversation_id": str(uuid.uuid4()),
        "created_at": datetime.now().isoformat()
    }
    
    result = supabase.table("chat_history").insert(chat_data).execute()
    return result
```

## 📤 9. Respuesta Final al Frontend

```python
# Estructura de respuesta
response = ChatResponse(
    reply="El precio actual de Tesla (TSLA) es $335.58. El último cierre fue el 14 de agosto a las 15:30 UTC.",
    timestamp=datetime.now(),
    conversation_id="conv_123456"
)

return response
```

## 🔄 10. Ejemplo Completo de Flujo

### Input del Usuario:
```
"¿Cuál es el precio de Tesla y su RSI?"
```

### Procesamiento:
1. **Frontend** → POST /api/v1/chat/ con JWT
2. **Backend** → Valida usuario
3. **Gemini** → Recibe: "¿Cuál es el precio de Tesla y su RSI?"
4. **Gemini** → Decide usar `get_price_data("NASDAQ:TSLA", "1D", 5)`
5. **TradingView** → Devuelve datos OHLCV
6. **Gemini** → Decide usar `apply_indicator("NASDAQ:TSLA", "1D", "RSI", {"length": 14})`
7. **pandas_ta** → Calcula RSI = 57.5
8. **Gemini** → Genera respuesta final
9. **Supabase** → Guarda historial
10. **Frontend** → Muestra respuesta al usuario

### Output Final:
```
"El precio actual de Tesla (TSLA) es $335.58, con un cierre el 14 de agosto. 
El RSI(14) está en 57.5, indicando una condición neutral. 
Esta información es solo para fines educativos."
```

---

## 🔧 Herramientas Disponibles para la IA

### 1. get_price_data
```json
{
  "name": "get_price_data",
  "description": "Obtiene datos históricos de precios OHLCV",
  "parameters": {
    "symbol": "NASDAQ:TSLA",
    "interval": "1D", 
    "n_bars": 10
  }
}
```

### 2. apply_indicator
```json
{
  "name": "apply_indicator", 
  "description": "Calcula indicadores técnicos",
  "parameters": {
    "symbol": "NASDAQ:TSLA",
    "interval": "1D",
    "indicator_name": "RSI",
    "parameters": {"length": 14}
  }
}
```

---

## 📋 Puntos Clave del Flujo

✅ **Autenticación:** JWT en cada request  
✅ **Historial:** Se mantiene contexto de conversación  
✅ **Function Calling:** Gemini decide cuándo usar herramientas  
✅ **Datos Reales:** TradingView proporciona información actualizada  
✅ **Persistencia:** Todo se guarda en Supabase  
✅ **Seguridad:** Validación en cada paso

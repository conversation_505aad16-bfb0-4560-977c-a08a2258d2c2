# Configuración de yfinance como Proveedor Principal de Datos

## 📦 Instalación

Para instalar yfinance como alternativa a tvDatafeed:

```bash
# Activar el entorno virtual
cd backend_api
source venv/bin/activate  # En Windows: venv\Scripts\activate

# Instalar yfinance
pip install yfinance

# Actualizar requirements.txt
pip freeze > requirements.txt
```

## 🔧 Configuración Implementada

### Proveedores de Datos (en orden de prioridad):

1. **yfinance** (Principal) - Yahoo Finance
   - ✅ Más estable y confiable
   - ✅ No requiere API key
   - ✅ Soporte para acciones, ETFs, criptomonedas
   - ✅ Datos históricos extensos

2. **tvDatafeed** (Fallback) - TradingView
   - ⚠️ API no oficial, puede fallar
   - ✅ Mantiene compatibilidad existente

## 🎯 Símbolos Soportados

### Acciones (yfinance)
- `TSLA` → Tesla
- `AAPL` → Apple  
- `MSFT` → Microsoft
- `GOOGL` → Google
- `AMZN` → Amazon

### Criptomonedas (yfinance)
- `BTCUSDT` → `BTC-USD` (Bitcoin)
- `ETHUSDT` → `ETH-USD` (Ethereum)
- `ADAUSDT` → `ADA-USD` (Cardano)

## 🔄 Flujo de Funcionamiento

```
Usuario: "precio de Tesla"
    ↓
1. Sistema detecta consulta → get_price_data("NASDAQ:TSLA")
    ↓
2. Intenta yfinance primero → yf.Ticker("TSLA").history()
    ↓
3a. SI yfinance funciona → Retorna datos
3b. SI yfinance falla → Intenta tvDatafeed (fallback)
    ↓
4. Procesa datos y genera respuesta
```

## ✅ Ventajas de la Nueva Configuración

1. **Mayor Confiabilidad**: yfinance es más estable que tvDatafeed
2. **Fallback Automático**: Si yfinance falla, usa tvDatafeed
3. **Sin Cambios en API**: La interfaz externa permanece igual
4. **Mejor Cobertura**: yfinance tiene mejor soporte para diferentes mercados
5. **Sin API Keys**: No requiere configuración adicional

## 🧪 Pruebas Recomendadas

Después de instalar yfinance, probar:

```bash
# Consultas que deberían funcionar con yfinance:
"precio de Tesla"
"precio de TSLA" 
"precio de Apple"
"precio de AAPL"
"precio de Microsoft"
"precio de Bitcoin"
```

## 📊 Intervalos Soportados

- `1m`, `5m`, `15m`, `30m` → Datos intraday
- `1h`, `4h` → Datos por horas
- `1D` → Datos diarios
- `1W` → Datos semanales  
- `1M` → Datos mensuales

## 🔍 Debugging

Para verificar qué proveedor se está usando, revisar los logs:

```
INFO:app.tools.tradingview_provider:Trying yfinance for NASDAQ:TSLA
INFO:app.tools.tradingview_provider:Falling back to tvDatafeed for NASDAQ:TSLA
```

## 🚀 Próximos Pasos

1. **Instalar yfinance**: `pip install yfinance`
2. **Probar con símbolos comunes**: TSLA, AAPL, MSFT
3. **Verificar logs** para confirmar que yfinance funciona
4. **Considerar Alpha Vantage** si se necesitan más funcionalidades

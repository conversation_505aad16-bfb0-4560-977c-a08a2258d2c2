#!/usr/bin/env python3
"""
Script simple para probar la IA.
"""

import sys
import asyncio
sys.path.append('app')

from app.services.vertex_ai import generate_chat_response, initialize_gemini_model
from app.tools.tradingview_provider import get_available_tools

async def main():
    print('🧪 === PRUEBA SIMPLE DE IA ===')
    
    try:
        # 1. Obtener herramientas
        print('1. Obteniendo herramientas...')
        tools = get_available_tools()
        print(f'   ✅ {len(tools)} herramientas disponibles')
        
        # 2. Inicializar modelo
        print('2. Inicializando modelo...')
        model = initialize_gemini_model(tools)
        
        if not model:
            print('   ❌ Modelo no inicializado')
            return False
        
        print('   ✅ Modelo inicializado')
        
        # 3. Probar respuesta simple
        print('3. Probando respuesta simple...')
        messages = [{'role': 'user', 'content': 'Hola'}]
        
        response = await generate_chat_response(messages, model)
        print(f'   ✅ Respuesta recibida: {type(response)}')
        print(f'   📝 Contenido: {response}')
        
        return True
        
    except Exception as e:
        print(f'❌ Error: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    print(f'\n{"🎉 ÉXITO" if success else "💥 FALLO"}')

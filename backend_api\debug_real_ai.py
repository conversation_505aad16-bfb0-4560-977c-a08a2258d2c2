#!/usr/bin/env python3
"""
Script para debuggear la interacción REAL con Vertex AI
Carga variables de entorno desde .env y hace llamadas reales
"""

import os
import sys
import asyncio
from pathlib import Path

# Cargar variables de entorno desde .env
def load_env_file():
    """Cargar variables de entorno desde .env"""
    env_file = Path('.env')
    if not env_file.exists():
        print("❌ Archivo .env no encontrado")
        return False
    
    print("📁 Cargando variables desde .env...")
    with open(env_file, 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                os.environ[key.strip()] = value.strip()
                print(f"✅ {key.strip()}: {'*' * min(len(value.strip()), 10)}")
    
    return True

# Configurar el path para imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

async def debug_real_ai():
    """Debuggear la IA real paso a paso"""
    
    print("🚀 === DEBUG REAL VERTEX AI ===\n")
    
    # 1. Cargar variables de entorno
    if not load_env_file():
        return
    
    # 2. Importar módulos
    print("\n🔍 Importando módulos...")
    try:
        from app.services.vertex_ai import initialize_gemini_model, generate_chat_response
        print("✅ Módulos importados correctamente")
    except Exception as e:
        print(f"❌ Error importando módulos: {e}")
        return
    
    # 3. Obtener herramientas e inicializar modelo REAL
    print("\n🔧 Obteniendo herramientas financieras...")
    try:
        from app.tools.tradingview_provider import get_available_tools
        tools = get_available_tools()
        print(f"✅ {len(tools)} herramientas cargadas: {[tool.__name__ for tool in tools]}")
    except Exception as e:
        print(f"❌ Error cargando herramientas: {e}")
        return

    print("\n🤖 Inicializando modelo Gemini REAL...")
    try:
        model = await initialize_gemini_model(tools)
        if model is None:
            print("❌ Modelo no se pudo inicializar")
            return
        print("✅ Modelo Gemini inicializado correctamente")
        print(f"Tipo: {type(model)}")
    except Exception as e:
        print(f"❌ Error inicializando modelo: {e}")
        import traceback
        print(traceback.format_exc())
        return
    
    # 4. Probar consulta financiera REAL
    print("\n💰 === PRUEBA REAL: CONSULTA FINANCIERA ===")
    
    messages = [
        {"role": "user", "content": "¿Cuál es el precio de Tesla?"}
    ]
    
    print(f"📝 Enviando a Gemini: {messages[0]['content']}")
    print("⏳ Esperando respuesta de Vertex AI...")
    
    try:
        result = await generate_chat_response(messages, model)
        
        print("\n📊 === RESULTADO DE VERTEX AI ===")
        print(f"Tipo de respuesta: {result.get('type')}")
        
        if result.get('type') == 'function_call':
            print("🎯 ¡ÉXITO! Gemini quiere usar una herramienta:")
            print(f"  Función: {result.get('function_name')}")
            print(f"  Argumentos: {result.get('function_args')}")
            print("✅ PROBLEMA RESUELTO - Las herramientas funcionan")
            
        elif result.get('type') == 'text':
            content = result.get('content', '')
            print(f"📝 Respuesta de texto: {content}")
            
            if "Lo siento, no pude procesar" in content:
                print("❌ PROBLEMA PERSISTE - Gemini devolvió fallback")
                print("🔍 Esto significa que el problema aún no está resuelto")
            else:
                print("✅ Respuesta de texto válida (no esperada para consulta financiera)")
        
        else:
            print(f"❓ Tipo de respuesta desconocido: {result}")
            
    except Exception as e:
        print(f"❌ ERROR en la llamada a Vertex AI: {e}")
        import traceback
        print(f"Traceback completo:\n{traceback.format_exc()}")
    
    # 5. Probar consulta general REAL
    print("\n💬 === PRUEBA REAL: CONSULTA GENERAL ===")
    
    messages_general = [
        {"role": "user", "content": "Hola, ¿qué es el análisis técnico?"}
    ]
    
    print(f"📝 Enviando a Gemini: {messages_general[0]['content']}")
    print("⏳ Esperando respuesta de Vertex AI...")
    
    try:
        result_general = await generate_chat_response(messages_general, model)
        
        print("\n📊 === RESULTADO DE VERTEX AI ===")
        print(f"Tipo de respuesta: {result_general.get('type')}")
        
        if result_general.get('type') == 'text':
            content = result_general.get('content', '')
            print(f"📝 Respuesta: {content[:200]}{'...' if len(content) > 200 else ''}")
            
            if "Lo siento, no pude procesar" in content:
                print("❌ PROBLEMA GRAVE - Incluso consultas generales fallan")
            else:
                print("✅ Consulta general funciona correctamente")
                
        elif result_general.get('type') == 'function_call':
            print("❓ Inesperado - Consulta general activó herramienta")
            print(f"  Función: {result_general.get('function_name')}")
            
    except Exception as e:
        print(f"❌ ERROR en consulta general: {e}")
        import traceback
        print(f"Traceback completo:\n{traceback.format_exc()}")
    
    print("\n🏁 === DIAGNÓSTICO COMPLETADO ===")

if __name__ == "__main__":
    try:
        asyncio.run(debug_real_ai())
    except KeyboardInterrupt:
        print("\n⏹️ Script interrumpido")
    except Exception as e:
        print(f"\n💥 Error inesperado: {e}")
        import traceback
        print(traceback.format_exc())

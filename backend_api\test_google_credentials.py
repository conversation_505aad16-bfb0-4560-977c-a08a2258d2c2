#!/usr/bin/env python3
"""
Script para probar las credenciales de Google directamente.
"""

import json
from google.oauth2 import service_account
from app.config import settings

def test_google_credentials():
    """Probar las credenciales de Google directamente."""
    print("🔐 === PRUEBA DIRECTA DE CREDENCIALES GOOGLE ===")
    
    if not settings.google_credentials_json:
        print("❌ No hay credenciales JSON configuradas")
        return False
    
    try:
        # 1. Parsear JSON
        print("1. Parseando JSON...")
        credentials_info = json.loads(settings.google_credentials_json)
        print(f"   ✅ JSON parseado correctamente")
        print(f"   📋 Project ID: {credentials_info.get('project_id')}")
        print(f"   📋 Client Email: {credentials_info.get('client_email')}")
        
        # 2. Verificar private_key
        print("\n2. Verificando private_key...")
        private_key = credentials_info.get('private_key', '')
        print(f"   📏 Longitud: {len(private_key)} caracteres")
        print(f"   🔍 Empieza con: {private_key[:30]}...")
        print(f"   🔍 Termina con: ...{private_key[-30:]}")
        
        # Verificar formato
        if private_key.startswith('-----BEGIN PRIVATE KEY-----'):
            print("   ✅ Formato de inicio correcto")
        else:
            print("   ❌ Formato de inicio incorrecto")
            
        if private_key.endswith('-----END PRIVATE KEY-----\n'):
            print("   ✅ Formato de final correcto")
        elif private_key.endswith('-----END PRIVATE KEY-----'):
            print("   ⚠️ Falta newline al final")
            # Agregar newline
            credentials_info['private_key'] = private_key + '\n'
            print("   🔧 Newline agregado")
        else:
            print("   ❌ Formato de final incorrecto")
        
        # 3. Intentar crear credenciales
        print("\n3. Creando objeto de credenciales...")
        try:
            credentials = service_account.Credentials.from_service_account_info(
                credentials_info,
                scopes=['https://www.googleapis.com/auth/cloud-platform']
            )
            print("   ✅ Credenciales creadas exitosamente")
            print(f"   📋 Service account email: {credentials.service_account_email}")
            print(f"   📋 Project ID: {credentials.project_id}")
            return True
            
        except Exception as e:
            print(f"   ❌ Error creando credenciales: {e}")
            print(f"   🔍 Tipo de error: {type(e).__name__}")
            
            # Intentar diagnóstico más específico
            if "No key could be detected" in str(e):
                print("   💡 El error 'No key could be detected' sugiere:")
                print("      - La private_key está malformada")
                print("      - Faltan caracteres en la clave")
                print("      - La clave no es válida")
                
                # Verificar contenido de la clave
                lines = private_key.split('\n')
                print(f"   📊 Líneas en private_key: {len(lines)}")
                if len(lines) > 2:
                    print(f"   🔍 Primera línea: {lines[0]}")
                    print(f"   🔍 Última línea: {lines[-2] if lines[-1] == '' else lines[-1]}")
                    
                    # Verificar que no haya líneas vacías en el medio
                    empty_lines = [i for i, line in enumerate(lines) if line.strip() == '' and i != len(lines)-1]
                    if empty_lines:
                        print(f"   ⚠️ Líneas vacías encontradas en posiciones: {empty_lines}")
            
            return False
            
    except json.JSONDecodeError as e:
        print(f"❌ Error parseando JSON: {e}")
        return False
    except Exception as e:
        print(f"❌ Error general: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_alternative_method():
    """Probar método alternativo usando archivo temporal."""
    print("\n🔄 === PRUEBA CON MÉTODO ALTERNATIVO ===")
    
    try:
        import tempfile
        import os
        
        # Crear archivo temporal
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            f.write(settings.google_credentials_json)
            temp_file = f.name
        
        print(f"📁 Archivo temporal creado: {temp_file}")
        
        try:
            # Intentar cargar desde archivo
            credentials = service_account.Credentials.from_service_account_file(
                temp_file,
                scopes=['https://www.googleapis.com/auth/cloud-platform']
            )
            print("✅ Credenciales cargadas desde archivo temporal")
            print(f"📋 Service account email: {credentials.service_account_email}")
            return True
            
        except Exception as e:
            print(f"❌ Error con archivo temporal: {e}")
            return False
            
        finally:
            # Limpiar archivo temporal
            os.unlink(temp_file)
            print("🗑️ Archivo temporal eliminado")
            
    except Exception as e:
        print(f"❌ Error en método alternativo: {e}")
        return False

if __name__ == "__main__":
    print("🧪 DIAGNÓSTICO COMPLETO DE CREDENCIALES GOOGLE CLOUD")
    print("=" * 60)
    
    success1 = test_google_credentials()
    success2 = test_alternative_method()
    
    print("\n" + "=" * 60)
    if success1 or success2:
        print("🎉 ¡Al menos un método funcionó!")
    else:
        print("💥 Ambos métodos fallaron")
        print("\n💡 Posibles soluciones:")
        print("   1. Regenerar las credenciales en Google Cloud Console")
        print("   2. Verificar que el service account tenga permisos de Vertex AI")
        print("   3. Verificar que la API de Vertex AI esté habilitada")
        print("   4. Usar un archivo de credenciales en lugar de JSON en variable")

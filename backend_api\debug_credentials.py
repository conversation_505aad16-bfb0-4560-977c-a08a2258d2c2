#!/usr/bin/env python3
"""
Script para diagnosticar problemas con las credenciales de Google Cloud.
"""

import os
import json
from app.config import settings

def debug_credentials():
    """Diagnosticar problemas con las credenciales."""
    print("🔍 === DIAGNÓSTICO DE CREDENCIALES GOOGLE CLOUD ===")
    
    # 1. Verificar variables de entorno
    print("\n1. Variables de entorno:")
    print(f"   GOOGLE_APPLICATION_CREDENTIALS: {os.getenv('GOOGLE_APPLICATION_CREDENTIALS', 'NO DEFINIDA')}")
    print(f"   GOOGLE_CREDENTIALS_JSON: {'DEFINIDA' if os.getenv('GOOGLE_CREDENTIALS_JSON') else 'NO DEFINIDA'}")
    
    # 2. Verificar configuración
    print("\n2. Configuración de settings:")
    print(f"   vertex_ai_project: {settings.vertex_ai_project}")
    print(f"   vertex_ai_location: {settings.vertex_ai_location}")
    print(f"   google_application_credentials: {settings.google_application_credentials}")
    print(f"   google_credentials_json: {'DEFINIDA' if settings.google_credentials_json else 'NO DEFINIDA'}")
    
    # 3. Verificar JSON de credenciales
    if settings.google_credentials_json:
        print("\n3. Análisis del JSON de credenciales:")
        try:
            # Mostrar primeros caracteres
            json_str = settings.google_credentials_json
            print(f"   Longitud: {len(json_str)} caracteres")
            print(f"   Primeros 100 caracteres: {json_str[:100]}...")
            print(f"   Últimos 50 caracteres: ...{json_str[-50:]}")
            
            # Buscar caracteres problemáticos
            problematic_chars = []
            for i, char in enumerate(json_str):
                if char == '\\' and i < len(json_str) - 1:
                    next_char = json_str[i + 1]
                    if next_char not in ['n', 't', 'r', '"', '\\', '/']:
                        problematic_chars.append((i, char + next_char))
            
            if problematic_chars:
                print(f"   ⚠️ Caracteres problemáticos encontrados: {problematic_chars[:5]}")
            else:
                print("   ✅ No se encontraron caracteres de escape problemáticos")
            
            # Intentar parsear
            print("\n4. Intentando parsear JSON:")
            try:
                # Primero intentar sin procesar
                json.loads(json_str)
                print("   ✅ JSON válido sin procesamiento")
            except json.JSONDecodeError as e:
                print(f"   ❌ Error sin procesamiento: {e}")
                
                # Intentar con reemplazo de \n
                try:
                    processed_json = json_str.replace('\\n', '\n')
                    parsed = json.loads(processed_json)
                    print("   ✅ JSON válido después de procesar \\n")
                    print(f"   Project ID: {parsed.get('project_id', 'NO ENCONTRADO')}")
                    print(f"   Client Email: {parsed.get('client_email', 'NO ENCONTRADO')}")
                except json.JSONDecodeError as e2:
                    print(f"   ❌ Error después de procesar \\n: {e2}")
                    print(f"   Posición del error: línea {e2.lineno}, columna {e2.colno}")
                    
                    # Mostrar contexto del error
                    if hasattr(e2, 'pos'):
                        start = max(0, e2.pos - 50)
                        end = min(len(processed_json), e2.pos + 50)
                        context = processed_json[start:end]
                        print(f"   Contexto del error: ...{context}...")
        
        except Exception as e:
            print(f"   ❌ Error general analizando JSON: {e}")
    
    else:
        print("\n3. No hay JSON de credenciales definido")
    
    # 4. Verificar archivo de credenciales
    if settings.google_application_credentials:
        print(f"\n4. Verificando archivo de credenciales: {settings.google_application_credentials}")
        try:
            if os.path.exists(settings.google_application_credentials):
                print("   ✅ Archivo existe")
                with open(settings.google_application_credentials, 'r') as f:
                    file_content = f.read()
                    try:
                        parsed = json.loads(file_content)
                        print(f"   ✅ JSON válido en archivo")
                        print(f"   Project ID: {parsed.get('project_id', 'NO ENCONTRADO')}")
                        print(f"   Client Email: {parsed.get('client_email', 'NO ENCONTRADO')}")
                    except json.JSONDecodeError as e:
                        print(f"   ❌ JSON inválido en archivo: {e}")
            else:
                print("   ❌ Archivo no existe")
        except Exception as e:
            print(f"   ❌ Error leyendo archivo: {e}")
    
    # 5. Recomendaciones
    print("\n5. Recomendaciones:")
    if not settings.google_credentials_json and not settings.google_application_credentials:
        print("   ⚠️ No hay credenciales configuradas")
        print("   💡 Configura GOOGLE_CREDENTIALS_JSON o GOOGLE_APPLICATION_CREDENTIALS")
    elif settings.google_credentials_json:
        print("   💡 Verifica que el JSON en GOOGLE_CREDENTIALS_JSON esté bien escapado")
        print("   💡 Asegúrate de que las barras invertidas estén duplicadas: \\\\n en lugar de \\n")
    else:
        print("   💡 Usando archivo de credenciales, verifica que sea válido")

if __name__ == "__main__":
    debug_credentials()

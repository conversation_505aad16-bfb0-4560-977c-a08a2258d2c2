# 🔍 Scripts de Diagnóstico TradingView

Este conjunto de scripts te permite verificar qué datos se están recibiendo de TradingView y diagnosticar posibles problemas de conectividad.

## 📋 Scripts Disponibles

### 1. `debug_tradingview.py` - Diagnóstico Completo
Script principal con múltiples opciones de diagnóstico.

**Uso básico:**
```bash
cd backend_api
python debug_tradingview.py
```

**Opciones avanzadas:**
```bash
# Probar símbolo específico
python debug_tradingview.py --symbol NASDAQ:AAPL --interval 1D --bars 10

# Probar múltiples símbolos
python debug_tradingview.py --test-multiple

# Probar indicadores técnicos
python debug_tradingview.py --test-indicators

# Probar nuestras funciones del módulo
python debug_tradingview.py --test-functions

# Modo verbose
python debug_tradingview.py --verbose

# Combinaciones
python debug_tradingview.py --test-multiple --test-indicators --verbose
```

### 2. `quick_test.py` - Prueba Rápida
Script simple para verificación rápida.

```bash
python quick_test.py
```

Este script:
- ✅ Verifica importación de tvDatafeed
- ✅ Prueba conexión básica
- ✅ Obtiene datos de TSLA
- ✅ Prueba nuestro módulo tradingview_provider
- ✅ Muestra salida JSON

### 3. `realtime_test.py` - Pruebas en Tiempo Real
Script interactivo para monitoreo en tiempo real.

```bash
python realtime_test.py
```

**Opciones del menú:**
1. **Monitorear símbolo** - Observa cambios de precio por 5 minutos
2. **Probar frescura de datos** - Verifica qué tan actualizados están los datos
3. **Probar diferentes intervalos** - Prueba 1m, 5m, 15m, 1h, 1D
4. **Modo interactivo** - Prueba símbolos manualmente
5. **Salir**

## 🚀 Inicio Rápido

### Paso 1: Preparar el entorno
```bash
cd backend_api

# Activar entorno virtual (si usas uno)
source venv/bin/activate  # Linux/Mac
# o
venv\Scripts\activate     # Windows

# Verificar dependencias
pip install tvdatafeed pandas pandas-ta
```

### Paso 2: Prueba rápida
```bash
python quick_test.py
```

### Paso 3: Diagnóstico completo
```bash
python debug_tradingview.py --test-multiple --test-indicators
```

## 📊 Interpretación de Resultados

### ✅ Resultados Exitosos
```
✅ tvDatafeed importado correctamente
✅ Cliente TvDatafeed inicializado
✅ Datos recibidos: 5 filas
📊 Columnas: ['open', 'high', 'low', 'close', 'volume']
💰 Último precio: $248.50
```

### ❌ Problemas Comunes

**1. Error de importación:**
```
❌ Error de importación: No module named 'tvdatafeed'
💡 Instala: pip install tvdatafeed
```

**2. Sin datos:**
```
❌ No se recibieron datos (None)
❌ DataFrame vacío
```
- Verifica que el símbolo sea correcto
- Prueba con símbolos populares (NASDAQ:TSLA, NASDAQ:AAPL)
- Verifica tu conexión a internet

**3. Datos antiguos:**
```
🔴 Tesla (NASDAQ:TSLA)
   Antigüedad: 120.5 minutos
```
- Normal fuera del horario de mercado
- Verifica la hora del mercado correspondiente

## 🔧 Solución de Problemas

### Problema: "No module named 'tvdatafeed'"
```bash
pip install tvdatafeed
```

### Problema: "No module named 'pandas_ta'"
```bash
pip install pandas-ta
```

### Problema: Datos inconsistentes
1. Ejecuta `python debug_tradingview.py --test-multiple`
2. Verifica qué símbolos funcionan
3. Usa símbolos que funcionen en tu aplicación

### Problema: Errores de conexión
1. Verifica tu conexión a internet
2. Prueba con diferentes símbolos
3. Espera unos minutos y vuelve a intentar

## 📈 Símbolos de Prueba Recomendados

### Acciones (Horario: 9:30-16:00 ET)
- `NASDAQ:TSLA` - Tesla
- `NASDAQ:AAPL` - Apple
- `NASDAQ:MSFT` - Microsoft
- `NYSE:SPY` - S&P 500 ETF

### Crypto (24/7)
- `BINANCE:BTCUSDT` - Bitcoin
- `BINANCE:ETHUSDT` - Ethereum
- `BINANCE:ADAUSDT` - Cardano

### Forex (24/5)
- `FOREX:EURUSD` - Euro/Dólar
- `FOREX:GBPUSD` - Libra/Dólar
- `FOREX:USDJPY` - Dólar/Yen

## 🕐 Horarios de Mercado

### Mercado de Valores US
- **Horario regular:** 9:30 AM - 4:00 PM ET
- **Pre-market:** 4:00 AM - 9:30 AM ET
- **After-hours:** 4:00 PM - 8:00 PM ET

### Criptomonedas
- **24/7** - Siempre activo

### Forex
- **24/5** - Lunes a Viernes

## 📝 Logs y Debugging

### Activar logs detallados:
```bash
python debug_tradingview.py --verbose
```

### Verificar configuración:
```python
from app.config import settings
print(settings.dict())
```

### Probar herramientas directamente:
```python
from app.tools.tradingview_provider import get_price_data
result = get_price_data('NASDAQ:TSLA', '1D', 5)
print(result)
```

## 🆘 Obtener Ayuda

Si los scripts muestran errores consistentes:

1. **Ejecuta el diagnóstico completo:**
   ```bash
   python debug_tradingview.py --test-multiple --test-indicators --verbose > diagnostico.log 2>&1
   ```

2. **Revisa el archivo `diagnostico.log`**

3. **Verifica la configuración del entorno**

4. **Prueba con símbolos diferentes**

## 📋 Checklist de Verificación

- [ ] tvdatafeed instalado correctamente
- [ ] pandas y pandas-ta disponibles
- [ ] Conexión a internet estable
- [ ] Al menos un símbolo devuelve datos
- [ ] Los datos tienen timestamps recientes
- [ ] Las funciones del módulo funcionan
- [ ] Los indicadores se calculan correctamente

---

**💡 Tip:** Ejecuta `python quick_test.py` regularmente para verificar que todo sigue funcionando correctamente.

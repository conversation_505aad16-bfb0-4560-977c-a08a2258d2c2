"""
Vertex AI service - Versión corregida y robustecida
Basado en los resultados de debug y análisis del problema con thought_signature y mensajes duplicados.
"""

import logging
import json
from typing import List, Dict, Any, Optional
from fastapi import HTTPException, status
import traceback

# Vertex AI imports
import vertexai
from vertexai.generative_models import GenerativeModel, Content, Part
import vertexai.generative_models as generative_models
from vertexai.generative_models import GenerativeModel, Content, Part

from app.config import settings

logger = logging.getLogger(__name__)

def initialize_gemini_model(tools: List[Any]) -> Optional[GenerativeModel]:
    """
    Initialize Gemini model with financial tools
    
    Args:
        tools: List of available financial tools
        
    Returns:
        GenerativeModel instance or None if initialization fails
    """
    try:
        print(f"🔧 Initializing Vertex AI...")
        print(f"🔧 Project: {settings.vertex_ai_project}")
        print(f"🔧 Location: {settings.vertex_ai_location}")
        
        # Setup Google Cloud credentials from JSON string
        if settings.google_credentials_json:
            from google.oauth2 import service_account

            print("🔐 Setting up Google Cloud credentials from JSON...")

            try:
                credentials_info = json.loads(settings.google_credentials_json)
                if 'private_key' in credentials_info and '\\n' in credentials_info['private_key']:
                    print("🔐 Converting literal \\n to actual newlines in private_key")
                    credentials_info['private_key'] = credentials_info['private_key'].replace('\\n', '\n')
            except json.JSONDecodeError as e:
                print(f"🔐 JSON parse failed, trying with newline replacement: {e}")
                credentials_json = settings.google_credentials_json.replace('\\n', '\n')
                credentials_info = json.loads(credentials_json)

            print(f"🔐 Credentials project_id: {credentials_info.get('project_id')}")
            credentials = service_account.Credentials.from_service_account_info(
                credentials_info,
                scopes=['https://www.googleapis.com/auth/cloud-platform']
            )

            vertexai.init(
                project=settings.vertex_ai_project,
                location=settings.vertex_ai_location,
                credentials=credentials
            )
            print("✅ Vertex AI initialized with service account credentials")
        else:
            print("⚠️ No Google credentials JSON found, using default authentication")
            vertexai.init(
                project=settings.vertex_ai_project,
                location=settings.vertex_ai_location
            )
        
        vertex_tools = []
        if tools:
            print(f"🔧 Converting {len(tools)} tools to Vertex AI format...")
            function_declarations = []
            for tool in tools:
                try:
                    if isinstance(tool, dict):
                        function_declarations.append(
                            generative_models.FunctionDeclaration(
                                name=tool["name"],
                                description=tool["description"],
                                parameters=tool["parameters"]
                            )
                        )
                except Exception as e:
                    logger.error(f"Error converting tool {tool}: {e}")
                    print(f"❌ Error converting tool: {e}")

            if function_declarations:
                vertex_tools.append(generative_models.Tool(function_declarations=function_declarations))
                print(f"✅ Combined tool created with {len(function_declarations)} functions")

        print(f"🔧 Creating model with {len(vertex_tools)} vertex tools")
        
        model = GenerativeModel(
            model_name="gemini-2.5-pro",  # Usar un modelo estable recomendado para tools
            tools=vertex_tools if vertex_tools else None,
            system_instruction="""Eres un asistente financiero especializado. Responde SIEMPRE en español.

IMPORTANTE: Siempre debes proporcionar una respuesta de texto al usuario, nunca dejes una respuesta vacía.

HERRAMIENTAS DISPONIBLES:
- get_price_data: Para obtener precios de acciones, criptomonedas, etc.
- apply_indicator: Para calcular indicadores técnicos como RSI, MACD, etc.

CUÁNDO USAR HERRAMIENTAS:
- Si el usuario pregunta por precios específicos → usa get_price_data
- Si el usuario pregunta por indicadores técnicos → usa apply_indicator
- Para preguntas generales → responde directamente

FORMATO DE RESPUESTA:
- Sé claro y directo
- Siempre incluye: "Esta información es solo para fines educativos"
- Responde en español conversacional, no en formato técnico"""
        )
        
        print(f"✅ Gemini model initialized successfully with {len(tools)} tools")
        logger.info(f"Gemini model initialized successfully with {len(tools)} tools")
        return model
        
    except Exception as e:
        logger.error(f"Error initializing Gemini model: {e}")
        print(f"❌ Error initializing Gemini model: {e}")
        return None

async def generate_chat_response(
    messages: List[Dict[str, str]],
    model: Optional[GenerativeModel]
) -> Dict[str, Any]:
    """
    Generate a chat response using the Gemini model.
    VERSIÓN ROBUSTA: Usa generate_content, maneja mensajes duplicados y respuestas de 'thought_signature'.
    """
    print("🚀 === GENERATE_CHAT_RESPONSE FUNCTION STARTED ===")
    logger.info("=== GENERATE_CHAT_RESPONSE FUNCTION STARTED ===")
    
    if model is None:
        print("⚠️ Vertex AI model not available, returning fallback response")
        logger.warning("Vertex AI model not available, returning fallback response")
        return {
            "type": "text",
            "content": "Lo siento, el servicio de IA no está disponible en este momento."
        }

    try:
        print(f"📨 === CONSTRUYENDO 'contents' A PARTIR DE {len(messages)} MENSAJES ===")
        contents_history = []
        for i, message in enumerate(messages):
            role = "model" if message.get("role") == "assistant" else "user"
            content = message.get("content", "").strip()

            print(f"📝 Mensaje {i+1}: role={role}, content='{content[:50]}...'")

            if not content:
                print(f"⚠️ Mensaje {i+1} vacío, saltando...")
                continue

            contents_history.append(Content(role=role, parts=[Part.from_text(content)]))

        if not contents_history:
            print("⚠️ No hay contenido válido para enviar.")
            return {"type": "text", "content": "Por favor, envía un mensaje."}

        print(f"📋 === HISTORIAL CONSTRUIDO CON {len(contents_history)} ELEMENTOS ===")

        # --- LÓGICA DE SEGURIDAD ANTI-DUPLICADOS (SOLO PARA CASOS EXTREMOS) ---
        if (len(contents_history) > 1 and
            contents_history[-1].role == 'user' and
            contents_history[-2].role == 'user' and
            contents_history[-1].parts[0].text == contents_history[-2].parts[0].text):
            print("🚮 De-duplicando el último mensaje del usuario que es idéntico al anterior.")
            print(f"🚮 Mensaje duplicado: '{contents_history[-1].parts[0].text[:50]}...'")
            contents_history.pop()
            print(f"📋 === DESPUÉS DE DEDUPLICACIÓN: {len(contents_history)} ELEMENTOS ===")

        print(f"🤖 === LLAMANDO A model.generate_content CON {len(contents_history)} PARTES ===")
        response = model.generate_content(contents_history)
        print("✅ === model.generate_content COMPLETED ===")

        if not response or not response.candidates:
            raise ValueError("Vertex AI no devolvió una respuesta válida (sin candidatos)")

        candidate = response.candidates[0]
        if not candidate.content or not candidate.content.parts:
            raise ValueError("El candidato de la respuesta no tiene contenido o partes válidas")

        print(f"🔍 === PROCESANDO {len(candidate.content.parts)} PARTES DE LA RESPUESTA ===")
        texts = []
        for i, part in enumerate(candidate.content.parts):
            # Prioridad 1: Buscar llamada a función
            if hasattr(part, 'function_call') and part.function_call:
                function_call = part.function_call
                print(f"🎯 === FUNCTION CALL DETECTED IN PART {i+1} ===")
                function_name = function_call.name
                function_args = {key: value for key, value in function_call.args.items()}
                logger.info(f"Function call detected: {function_name} with args {function_args}")
                return {
                    "type": "function_call",
                    "function_name": function_name,
                    "function_args": function_args
                }
            
            # Prioridad 2: Recolectar texto
            if hasattr(part, 'text') and part.text and part.text.strip():
                print(f"📝 Text found in part {i+1}: '{part.text[:70]}...'")
                texts.append(part.text)

        if texts:
            combined_text = " ".join(texts).strip()
            print(f"✅ === RETURNING TEXT RESPONSE: '{combined_text[:100]}...' ===")
            return {"type": "text", "content": combined_text}

        # --- LÓGICA DE FALLBACK PARA RESPUESTAS SIN CONTENIDO VISIBLE ---
        # Verificar si solo hay thought_signature sin texto
        has_only_thought = False
        if len(candidate.content.parts) > 0:
            for part in candidate.content.parts:
                has_thought = hasattr(part, 'thought_signature') and part.thought_signature
                has_text = hasattr(part, 'text') and part.text and part.text.strip()
                has_function_call = hasattr(part, 'function_call') and part.function_call

                # Si solo hay thought_signature sin texto ni function_call
                if has_thought and not has_text and not has_function_call:
                    has_only_thought = True
                    break

        if has_only_thought:
            print("🧠 === THOUGHT_SIGNATURE ONLY RESPONSE DETECTED. INTENTANDO SEGUNDA LLAMADA. ===")
            logger.info("Thought-only response detected. Trying second attempt with simpler prompt.")

            # Intentar una segunda llamada con un prompt más directo
            try:
                simple_prompt = "Responde de forma clara y directa en español. Si el usuario pregunta por datos financieros específicos, usa las herramientas disponibles."
                simple_content = [Content(role="user", parts=[Part.from_text(simple_prompt)])]

                print("🔄 === SEGUNDA LLAMADA CON PROMPT SIMPLIFICADO ===")
                second_response = model.generate_content(simple_content)

                if second_response and second_response.candidates and second_response.candidates[0].content.parts:
                    for part in second_response.candidates[0].content.parts:
                        if hasattr(part, 'text') and part.text and part.text.strip():
                            print("✅ === SEGUNDA LLAMADA EXITOSA ===")
                            return {"type": "text", "content": part.text.strip()}

            except Exception as e:
                print(f"⚠️ Segunda llamada falló: {e}")

            # Fallback final si la segunda llamada también falla
            print("🧠 === USANDO FALLBACK DESPUÉS DE SEGUNDA LLAMADA ===")
            fallback_text = "¡Hola! Soy tu asistente financiero. Puedo ayudarte con precios de acciones, criptomonedas, indicadores técnicos y análisis de mercado. ¿En qué puedo asistirte hoy? Esta información es solo para fines educativos."
            return {"type": "text", "content": fallback_text}

        # Fallback final si todo lo demás falla
        print("❌ === NO VALID CONTENT FOUND. RETURNING FINAL FALLBACK. ===")
        logger.warning(f"No valid content found in response. Candidate: {candidate}")
        return {
            "type": "text",
            "content": "Lo siento, no pude procesar tu solicitud. Por favor, intenta reformular tu pregunta."
        }

    except Exception as e:
        print(f"💥 === EXCEPTION IN GENERATE_CHAT_RESPONSE: {str(e)} ===")
        logger.error(f"Exception in generate_chat_response: {str(e)}")
        traceback_str = traceback.format_exc()
        print(f"💥 Traceback: {traceback_str}")
        logger.error(f"Traceback: {traceback_str}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate AI response: {str(e)}"
        )


async def process_function_call_result(
    function_name: str,
    function_result: Any,
    conversation_history: List[Dict[str, str]],  # Cambiado para usar lista estructurada
    model: GenerativeModel
) -> str:
    """
    Process the result of a function call and generate a final response.
    Usa un enfoque estructurado con Content para mejor contexto.
    """
    try:
        # Construir historial estructurado
        contents_history = []
        for message in conversation_history:
            role = "model" if message.get("role") == "assistant" else "user"
            content = message.get("content", "").strip()
            if content:
                contents_history.append(Content(role=role, parts=[Part.from_text(content)]))

        # Añadir el resultado de la función como respuesta del modelo
        function_response = f"""Resultado de la función {function_name}:
{json.dumps(function_result, indent=2, ensure_ascii=False)}

Por favor, analiza estos datos y proporciona una respuesta clara y útil al usuario en español.
Recuerda incluir el descargo de responsabilidad sobre asesoramiento financiero."""

        contents_history.append(Content(role="model", parts=[Part.from_text(function_response)]))

        # Añadir solicitud para generar respuesta final
        contents_history.append(Content(role="user", parts=[Part.from_text(
            "Analiza los datos anteriores y proporciona una respuesta clara y útil en español."
        )]))

        print("🤖 === CALLING model.generate_content FOR FUNCTION RESULT WITH STRUCTURED CONTENT ===")
        response = model.generate_content(contents_history)

        if response.text:
            print("✅ Returning structured function result text")
            return response.text

        # Fallback si response.text está vacío pero hay partes con texto
        if response.candidates and response.candidates[0].content.parts:
            texts = [part.text for part in response.candidates[0].content.parts if hasattr(part, 'text') and part.text]
            if texts:
                return " ".join(texts).strip()

        print("⚠️ No valid text found in function result response, using fallback")
        return "He procesado la información solicitada, pero no pude generar una respuesta clara. Esta información es solo para fines educativos."

    except Exception as e:
        logger.error(f"Error processing function call result: {str(e)}")
        print(f"❌ Error processing function call result: {str(e)}")
        return f"Error procesando el resultado de {function_name}. Por favor, intenta de nuevo."
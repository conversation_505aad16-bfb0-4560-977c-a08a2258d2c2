#!/usr/bin/env python3
"""
Script para verificar la estructura de las credenciales de Google Cloud.
"""

import json
from app.config import settings

def verify_credentials_structure():
    """Verificar la estructura de las credenciales."""
    print("🔍 === VERIFICACIÓN DE ESTRUCTURA DE CREDENCIALES ===")
    
    if not settings.google_credentials_json:
        print("❌ No hay credenciales JSON configuradas")
        return False
    
    try:
        # Parsear JSON
        creds = json.loads(settings.google_credentials_json)
        
        # Campos requeridos para service account
        required_fields = [
            'type',
            'project_id', 
            'private_key_id',
            'private_key',
            'client_email',
            'client_id',
            'auth_uri',
            'token_uri'
        ]
        
        print("📋 Campos encontrados:")
        missing_fields = []
        
        for field in required_fields:
            if field in creds:
                if field == 'private_key':
                    # Solo mostrar inicio y fin de la clave privada
                    key_value = creds[field]
                    if len(key_value) > 50:
                        display_value = f"{key_value[:25]}...{key_value[-25:]}"
                    else:
                        display_value = "CLAVE_CORTA"
                    print(f"   ✅ {field}: {display_value}")
                    
                    # Verificar formato de la clave privada
                    if not key_value.startswith('-----BEGIN PRIVATE KEY-----'):
                        print(f"   ⚠️ La private_key no tiene el formato correcto")
                        print(f"   💡 Debe empezar con '-----BEGIN PRIVATE KEY-----'")
                        print(f"   🔍 Actualmente empieza con: '{key_value[:50]}...'")
                    
                    if not key_value.endswith('-----END PRIVATE KEY-----\n'):
                        print(f"   ⚠️ La private_key no termina correctamente")
                        print(f"   💡 Debe terminar con '-----END PRIVATE KEY-----\\n'")
                        print(f"   🔍 Actualmente termina con: '...{key_value[-50:]}'")
                        
                else:
                    print(f"   ✅ {field}: {creds[field]}")
            else:
                print(f"   ❌ {field}: FALTANTE")
                missing_fields.append(field)
        
        # Verificar campos adicionales
        additional_fields = ['auth_provider_x509_cert_url', 'client_x509_cert_url', 'universe_domain']
        print("\n📋 Campos adicionales:")
        for field in additional_fields:
            if field in creds:
                print(f"   ✅ {field}: {creds[field]}")
            else:
                print(f"   ⚠️ {field}: FALTANTE (opcional)")
        
        # Resumen
        print(f"\n📊 Resumen:")
        print(f"   Campos requeridos: {len(required_fields) - len(missing_fields)}/{len(required_fields)}")
        print(f"   Tipo de cuenta: {creds.get('type', 'DESCONOCIDO')}")
        print(f"   Proyecto: {creds.get('project_id', 'DESCONOCIDO')}")
        print(f"   Email del cliente: {creds.get('client_email', 'DESCONOCIDO')}")
        
        if missing_fields:
            print(f"   ❌ Campos faltantes: {missing_fields}")
            return False
        else:
            print(f"   ✅ Todos los campos requeridos están presentes")
            
            # Verificar formato de private_key específicamente
            private_key = creds.get('private_key', '')
            if private_key.startswith('-----BEGIN PRIVATE KEY-----') and private_key.endswith('-----END PRIVATE KEY-----\n'):
                print(f"   ✅ Formato de private_key correcto")
                return True
            else:
                print(f"   ❌ Formato de private_key incorrecto")
                return False
        
    except json.JSONDecodeError as e:
        print(f"❌ Error parseando JSON: {e}")
        return False
    except Exception as e:
        print(f"❌ Error general: {e}")
        return False

if __name__ == "__main__":
    success = verify_credentials_structure()
    if success:
        print("\n🎉 ¡Credenciales tienen estructura correcta!")
    else:
        print("\n💥 Hay problemas con la estructura de las credenciales")
        print("\n💡 Soluciones:")
        print("   1. Descarga nuevamente el archivo JSON desde Google Cloud Console")
        print("   2. Verifica que sea un archivo de 'service account'")
        print("   3. Asegúrate de copiar TODO el contenido del archivo")
        print("   4. Verifica que no haya caracteres extra al inicio o final")

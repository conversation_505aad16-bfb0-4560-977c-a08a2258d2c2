#!/usr/bin/env python3
"""
Script para probar datos en tiempo real de TradingView.

Monitorea continuamente los datos para verificar actualizaciones.
"""

import time
import json
from datetime import datetime
from typing import Dict, Any

def monitor_symbol(symbol: str = "NASDAQ:TSLA", duration_minutes: int = 5):
    """Monitorear un símbolo por un período de tiempo."""
    print(f"📡 Monitoreando {symbol} por {duration_minutes} minutos...")
    print(f"Inicio: {datetime.now()}")
    print("-" * 60)
    
    try:
        from tvDatafeed import TvDatafeed, Interval
        tv = TvDatafeed()
        
        # Parsear símbolo
        if ":" in symbol:
            exchange, ticker = symbol.split(":", 1)
        else:
            exchange = "NASDAQ"
            ticker = symbol
        
        start_time = time.time()
        end_time = start_time + (duration_minutes * 60)
        
        last_price = None
        check_count = 0
        
        while time.time() < end_time:
            check_count += 1
            current_time = datetime.now()
            
            try:
                # Obtener datos más recientes
                data = tv.get_hist(
                    symbol=ticker,
                    exchange=exchange,
                    interval=Interval.in_1_minute,
                    n_bars=1
                )
                
                if data is not None and not data.empty:
                    current_price = data['close'].iloc[-1]
                    data_time = data.index[-1]
                    
                    # Verificar si el precio cambió
                    price_changed = last_price is None or current_price != last_price
                    change_indicator = "🔄" if price_changed else "⏸️"
                    
                    print(f"{change_indicator} [{check_count:3d}] {current_time.strftime('%H:%M:%S')} | "
                          f"Precio: ${current_price:.2f} | "
                          f"Datos de: {data_time.strftime('%H:%M:%S')}")
                    
                    if price_changed and last_price is not None:
                        change = current_price - last_price
                        change_pct = (change / last_price) * 100
                        direction = "📈" if change > 0 else "📉" if change < 0 else "➡️"
                        print(f"    {direction} Cambio: ${change:+.2f} ({change_pct:+.2f}%)")
                    
                    last_price = current_price
                    
                else:
                    print(f"❌ [{check_count:3d}] {current_time.strftime('%H:%M:%S')} | Sin datos")
                
            except Exception as e:
                print(f"⚠️ [{check_count:3d}] {current_time.strftime('%H:%M:%S')} | Error: {e}")
            
            # Esperar 30 segundos antes del siguiente check
            time.sleep(30)
        
        print(f"\n✅ Monitoreo completado. Total de checks: {check_count}")
        
    except Exception as e:
        print(f"❌ Error en monitoreo: {e}")

def test_data_freshness():
    """Probar qué tan frescos son los datos."""
    print("🕐 Probando frescura de datos...")
    print("-" * 40)
    
    try:
        from tvDatafeed import TvDatafeed, Interval
        tv = TvDatafeed()
        
        symbols_to_test = [
            ("NASDAQ:TSLA", "Tesla"),
            ("NASDAQ:AAPL", "Apple"), 
            ("NYSE:SPY", "S&P 500 ETF"),
            ("BINANCE:BTCUSDT", "Bitcoin")
        ]
        
        current_time = datetime.now()
        print(f"Hora actual: {current_time}")
        print()
        
        for symbol, name in symbols_to_test:
            try:
                exchange, ticker = symbol.split(":")
                
                # Obtener datos de 1 minuto
                data = tv.get_hist(
                    symbol=ticker,
                    exchange=exchange,
                    interval=Interval.in_1_minute,
                    n_bars=5
                )
                
                if data is not None and not data.empty:
                    latest_time = data.index[-1]
                    latest_price = data['close'].iloc[-1]
                    
                    # Calcular diferencia de tiempo
                    time_diff = current_time - latest_time
                    minutes_old = time_diff.total_seconds() / 60
                    
                    freshness = "🟢" if minutes_old < 5 else "🟡" if minutes_old < 60 else "🔴"
                    
                    print(f"{freshness} {name} ({symbol})")
                    print(f"   Precio: ${latest_price:.2f}")
                    print(f"   Datos de: {latest_time}")
                    print(f"   Antigüedad: {minutes_old:.1f} minutos")
                    print()
                    
                else:
                    print(f"❌ {name} ({symbol}): Sin datos")
                    print()
                    
            except Exception as e:
                print(f"⚠️ {name} ({symbol}): Error - {e}")
                print()
        
    except Exception as e:
        print(f"❌ Error general: {e}")

def test_different_intervals():
    """Probar diferentes intervalos de tiempo."""
    print("⏱️ Probando diferentes intervalos...")
    print("-" * 40)
    
    try:
        from tvDatafeed import TvDatafeed, Interval
        tv = TvDatafeed()
        
        intervals_to_test = [
            (Interval.in_1_minute, "1 minuto"),
            (Interval.in_5_minute, "5 minutos"),
            (Interval.in_15_minute, "15 minutos"),
            (Interval.in_1_hour, "1 hora"),
            (Interval.in_daily, "Diario")
        ]
        
        symbol = "TSLA"
        exchange = "NASDAQ"
        
        for interval, name in intervals_to_test:
            try:
                data = tv.get_hist(
                    symbol=symbol,
                    exchange=exchange,
                    interval=interval,
                    n_bars=3
                )
                
                if data is not None and not data.empty:
                    latest_time = data.index[-1]
                    latest_price = data['close'].iloc[-1]
                    
                    print(f"✅ {name}: ${latest_price:.2f} ({latest_time})")
                else:
                    print(f"❌ {name}: Sin datos")
                    
            except Exception as e:
                print(f"⚠️ {name}: Error - {e}")
        
    except Exception as e:
        print(f"❌ Error general: {e}")

def interactive_test():
    """Prueba interactiva."""
    print("🎮 Modo interactivo")
    print("Escribe 'quit' para salir")
    print("-" * 30)
    
    try:
        from tvDatafeed import TvDatafeed, Interval
        tv = TvDatafeed()
        
        while True:
            try:
                symbol_input = input("\nSímbolo (ej: NASDAQ:TSLA): ").strip()
                
                if symbol_input.lower() == 'quit':
                    break
                
                if not symbol_input:
                    symbol_input = "NASDAQ:TSLA"
                
                # Parsear símbolo
                if ":" in symbol_input:
                    exchange, ticker = symbol_input.split(":", 1)
                else:
                    exchange = "NASDAQ"
                    ticker = symbol_input
                
                print(f"Obteniendo datos para {exchange}:{ticker}...")
                
                data = tv.get_hist(
                    symbol=ticker,
                    exchange=exchange,
                    interval=Interval.in_daily,
                    n_bars=5
                )
                
                if data is not None and not data.empty:
                    print(f"✅ Datos obtenidos: {len(data)} filas")
                    print(data.tail(2).to_string())
                else:
                    print("❌ No se obtuvieron datos")
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"⚠️ Error: {e}")
        
        print("\n👋 Saliendo del modo interactivo")
        
    except Exception as e:
        print(f"❌ Error en modo interactivo: {e}")

def main():
    """Función principal."""
    print("🧪 PRUEBAS EN TIEMPO REAL - TRADINGVIEW")
    print("=" * 50)
    
    while True:
        print("\nOpciones:")
        print("1. Monitorear símbolo (5 min)")
        print("2. Probar frescura de datos")
        print("3. Probar diferentes intervalos")
        print("4. Modo interactivo")
        print("5. Salir")
        
        choice = input("\nElige una opción (1-5): ").strip()
        
        if choice == "1":
            symbol = input("Símbolo (default: NASDAQ:TSLA): ").strip()
            if not symbol:
                symbol = "NASDAQ:TSLA"
            monitor_symbol(symbol, 5)
            
        elif choice == "2":
            test_data_freshness()
            
        elif choice == "3":
            test_different_intervals()
            
        elif choice == "4":
            interactive_test()
            
        elif choice == "5":
            print("👋 ¡Hasta luego!")
            break
            
        else:
            print("⚠️ Opción no válida")

if __name__ == "__main__":
    main()

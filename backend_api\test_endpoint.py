#!/usr/bin/env python3
"""
Test directo del endpoint de chat.
"""

import sys
import asyncio
import json
sys.path.append('app')

from fastapi.testclient import TestClient
from app.main import app

def test_chat_endpoint():
    """Probar el endpoint de chat directamente."""
    print('🧪 === TEST DIRECTO DEL ENDPOINT ===')
    
    try:
        # Crear cliente de prueba
        client = TestClient(app)
        
        # Datos de prueba
        chat_request = {
            "message": "Hola",
            "history": []
        }
        
        print('📡 Enviando request al endpoint /api/v1/chat/debug...')
        
        # Hacer request al endpoint de debug (sin autenticación)
        response = client.post("/api/v1/chat/debug", json=chat_request)
        
        print(f'📊 Status Code: {response.status_code}')
        print(f'📊 Headers: {dict(response.headers)}')
        
        if response.status_code == 200:
            print('✅ Request exitoso!')
            response_data = response.json()
            print(f'📝 Respuesta: {json.dumps(response_data, indent=2)}')
            return True
        else:
            print(f'❌ Request falló: {response.status_code}')
            print(f'❌ Error: {response.text}')
            return False
            
    except Exception as e:
        print(f'❌ Error en test: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_chat_endpoint()
    print(f'\n{"🎉 ÉXITO" if success else "💥 FALLO"}')

#!/usr/bin/env python3
"""
Script de prueba rápida para TradingView.

Uso rápido para verificar si los datos están llegando correctamente.
"""

import json
from datetime import datetime

def quick_test():
    """Prueba rápida de conectividad."""
    print("🚀 Prueba rápida de TradingView")
    print(f"Hora: {datetime.now()}")
    print("-" * 50)
    
    try:
        # Importar tvDatafeed
        print("1. Importando tvDatafeed...")
        from tvDatafeed import TvDatafeed, Interval
        print("   ✅ Importación exitosa")
        
        # Crear cliente
        print("2. Creando cliente...")
        tv = TvDatafeed()
        print("   ✅ Cliente creado")
        
        # Obtener datos de Tesla
        print("3. Obteniendo datos de TSLA...")
        data = tv.get_hist(
            symbol="TSLA",
            exchange="NASDAQ", 
            interval=Interval.in_daily,
            n_bars=5
        )
        
        if data is None:
            print("   ❌ No se recibieron datos (None)")
            return False
            
        if data.empty:
            print("   ❌ DataFrame vacío")
            return False
            
        print(f"   ✅ Datos recibidos: {len(data)} filas")
        print(f"   📊 Columnas: {list(data.columns)}")
        
        # Mostrar último precio
        if not data.empty:
            last_close = data['close'].iloc[-1]
            last_date = data.index[-1]
            print(f"   💰 Último precio: ${last_close:.2f}")
            print(f"   📅 Fecha: {last_date}")
        
        # Mostrar datos raw
        print("\n4. Datos raw (últimas 2 filas):")
        print(data.tail(2).to_string())
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Error de importación: {e}")
        print("   💡 Instala: pip install tvdatafeed")
        return False
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_our_module():
    """Probar nuestro módulo."""
    print("\n" + "="*50)
    print("🔧 Probando módulo tradingview_provider")
    print("="*50)
    
    try:
        import sys
        sys.path.append('app')
        
        from app.tools.tradingview_provider import get_price_data
        
        print("1. Probando get_price_data...")
        result = get_price_data("NASDAQ:TSLA", "1D", 3)
        
        print(f"   ✅ Símbolo: {result['symbol']}")
        print(f"   📊 Barras: {result['bars_count']}")
        print(f"   💰 Último precio: ${result['latest_price']:.2f}")
        print(f"   📅 Última fecha: {result['latest_datetime']}")
        
        # Mostrar estructura de datos
        print("\n2. Estructura de datos:")
        sample_bar = result['data'][0] if result['data'] else {}
        for key, value in sample_bar.items():
            print(f"   {key}: {value} ({type(value).__name__})")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        print(f"   🔍 Traceback: {traceback.format_exc()}")
        return False

def test_json_output():
    """Probar salida JSON."""
    print("\n" + "="*50)
    print("📄 Probando salida JSON")
    print("="*50)
    
    try:
        import sys
        sys.path.append('app')
        
        from app.tools.tradingview_provider import get_price_data
        
        result = get_price_data("NASDAQ:AAPL", "1D", 2)
        
        # Convertir a JSON
        json_output = json.dumps(result, indent=2, default=str)
        print("JSON Output:")
        print(json_output)
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 DIAGNÓSTICO RÁPIDO TRADINGVIEW")
    print("=" * 60)
    
    # Prueba básica
    basic_ok = quick_test()
    
    if basic_ok:
        # Prueba de nuestro módulo
        module_ok = test_our_module()
        
        if module_ok:
            # Prueba JSON
            test_json_output()
    
    print("\n" + "="*60)
    print("✅ Diagnóstico completado")

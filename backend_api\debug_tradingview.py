#!/usr/bin/env python3
"""
Script de diagnóstico para verificar datos de TradingView.

Este script permite verificar qué datos se están recibiendo del proveedor
de TradingView y diagnosticar posibles problemas de conectividad o formato.

Uso:
    python debug_tradingview.py
    python debug_tradingview.py --symbol NASDAQ:TSLA --interval 1D --bars 10
    python debug_tradingview.py --test-indicators
    python debug_tradingview.py --verbose
"""

import argparse
import sys
import json
import logging
from datetime import datetime
from typing import Dict, Any, List

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def setup_environment():
    """Configurar el entorno y verificar dependencias."""
    try:
        # Verificar tvDatafeed
        from tvDatafeed import TvDatafeed, Interval
        logger.info("✅ tvDatafeed importado correctamente")
        
        # Verificar pandas_ta
        try:
            import pandas_ta as ta
            logger.info("✅ pandas_ta disponible")
            pandas_ta_available = True
        except ImportError:
            logger.warning("⚠️ pandas_ta no disponible - indicadores técnicos no funcionarán")
            pandas_ta_available = False
        
        # Verificar pandas
        import pandas as pd
        logger.info("✅ pandas disponible")
        
        return True, pandas_ta_available
        
    except ImportError as e:
        logger.error(f"❌ Error importando dependencias: {e}")
        return False, False

def test_basic_connection():
    """Probar conexión básica con TradingView."""
    logger.info("\n🔍 Probando conexión básica con TradingView...")
    
    try:
        from tvDatafeed import TvDatafeed
        tv = TvDatafeed()
        logger.info("✅ Cliente TvDatafeed inicializado")
        return tv
    except Exception as e:
        logger.error(f"❌ Error inicializando cliente: {e}")
        return None

def test_symbol_data(tv, symbol: str = "NASDAQ:TSLA", interval: str = "1D", n_bars: int = 5):
    """Probar obtención de datos para un símbolo específico."""
    logger.info(f"\n📊 Probando datos para {symbol} ({interval}, {n_bars} barras)...")
    
    try:
        from tvDatafeed import Interval
        
        # Mapeo de intervalos
        interval_map = {
            "1m": Interval.in_1_minute,
            "5m": Interval.in_5_minute,
            "15m": Interval.in_15_minute,
            "30m": Interval.in_30_minute,
            "1h": Interval.in_1_hour,
            "4h": Interval.in_4_hour,
            "1D": Interval.in_daily,
            "1W": Interval.in_weekly,
            "1M": Interval.in_monthly
        }
        
        # Parsear símbolo
        if ":" in symbol:
            exchange, ticker = symbol.split(":", 1)
        else:
            exchange = "NASDAQ"
            ticker = symbol
        
        logger.info(f"Exchange: {exchange}, Ticker: {ticker}")
        
        # Obtener datos
        data = tv.get_hist(
            symbol=ticker,
            exchange=exchange,
            interval=interval_map.get(interval, Interval.in_daily),
            n_bars=n_bars
        )
        
        if data is None:
            logger.error("❌ No se recibieron datos (None)")
            return None
        
        if data.empty:
            logger.error("❌ DataFrame vacío")
            return None
        
        logger.info(f"✅ Datos recibidos: {len(data)} filas")
        logger.info(f"Columnas: {list(data.columns)}")
        logger.info(f"Índice: {type(data.index)} - {data.index.name}")
        logger.info(f"Rango de fechas: {data.index.min()} a {data.index.max()}")
        
        # Mostrar primeras filas
        logger.info("\n📋 Primeras 3 filas:")
        print(data.head(3).to_string())
        
        # Mostrar últimas filas
        logger.info("\n📋 Últimas 3 filas:")
        print(data.tail(3).to_string())
        
        # Verificar tipos de datos
        logger.info(f"\n🔍 Tipos de datos:")
        for col in data.columns:
            logger.info(f"  {col}: {data[col].dtype}")
        
        # Verificar valores nulos
        null_counts = data.isnull().sum()
        if null_counts.any():
            logger.warning(f"⚠️ Valores nulos encontrados:")
            for col, count in null_counts.items():
                if count > 0:
                    logger.warning(f"  {col}: {count} valores nulos")
        else:
            logger.info("✅ No hay valores nulos")
        
        return data
        
    except Exception as e:
        logger.error(f"❌ Error obteniendo datos: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None

def test_multiple_symbols(tv):
    """Probar múltiples símbolos populares."""
    logger.info("\n🎯 Probando múltiples símbolos...")
    
    test_symbols = [
        "NASDAQ:TSLA",
        "NASDAQ:AAPL", 
        "NYSE:SPY",
        "BINANCE:BTCUSDT",
        "FOREX:EURUSD"
    ]
    
    results = {}
    
    for symbol in test_symbols:
        try:
            logger.info(f"\nProbando {symbol}...")
            data = test_symbol_data(tv, symbol, "1D", 3)
            results[symbol] = data is not None and not data.empty
            
            if results[symbol]:
                logger.info(f"✅ {symbol}: OK")
            else:
                logger.error(f"❌ {symbol}: FALLO")
                
        except Exception as e:
            logger.error(f"❌ {symbol}: Error - {e}")
            results[symbol] = False
    
    # Resumen
    logger.info(f"\n📊 Resumen de símbolos:")
    successful = sum(results.values())
    total = len(results)
    logger.info(f"Exitosos: {successful}/{total}")
    
    return results

def test_indicators(tv, pandas_ta_available: bool):
    """Probar cálculo de indicadores técnicos."""
    if not pandas_ta_available:
        logger.warning("⚠️ pandas_ta no disponible - saltando pruebas de indicadores")
        return
    
    logger.info("\n📈 Probando indicadores técnicos...")
    
    try:
        import pandas_ta as ta
        
        # Obtener datos de prueba
        data = test_symbol_data(tv, "NASDAQ:TSLA", "1D", 50)
        if data is None or data.empty:
            logger.error("❌ No se pueden probar indicadores sin datos")
            return
        
        # Probar diferentes indicadores
        indicators_to_test = [
            ("RSI", lambda df: ta.rsi(df["close"], length=14)),
            ("SMA", lambda df: ta.sma(df["close"], length=20)),
            ("EMA", lambda df: ta.ema(df["close"], length=20)),
            ("MACD", lambda df: ta.macd(df["close"])),
            ("BBANDS", lambda df: ta.bbands(df["close"], length=20)),
        ]
        
        for name, calc_func in indicators_to_test:
            try:
                logger.info(f"\nProbando {name}...")
                result = calc_func(data)
                
                if result is not None:
                    if hasattr(result, 'shape'):
                        logger.info(f"✅ {name}: {result.shape}")
                        # Mostrar últimos valores no nulos
                        if hasattr(result, 'dropna'):
                            last_values = result.dropna().tail(3)
                            logger.info(f"Últimos valores: {last_values.tolist()}")
                    else:
                        logger.info(f"✅ {name}: Calculado")
                else:
                    logger.error(f"❌ {name}: Resultado None")
                    
            except Exception as e:
                logger.error(f"❌ {name}: Error - {e}")
    
    except Exception as e:
        logger.error(f"❌ Error general en indicadores: {e}")

def test_our_functions():
    """Probar nuestras funciones del módulo tradingview_provider."""
    logger.info("\n🔧 Probando funciones del módulo tradingview_provider...")
    
    try:
        # Importar nuestras funciones
        sys.path.append('app')
        from app.tools.tradingview_provider import get_price_data, apply_indicator
        
        # Probar get_price_data
        logger.info("\nProbando get_price_data...")
        try:
            result = get_price_data("NASDAQ:TSLA", "1D", 5)
            logger.info(f"✅ get_price_data: {result['bars_count']} barras")
            logger.info(f"Último precio: {result.get('latest_price', 'N/A')}")
        except Exception as e:
            logger.error(f"❌ get_price_data: {e}")
        
        # Probar apply_indicator
        logger.info("\nProbando apply_indicator...")
        try:
            result = apply_indicator("NASDAQ:TSLA", "1D", "RSI", {"length": 14})
            logger.info(f"✅ apply_indicator RSI: {len(result.get('values', []))} valores")
            if 'latest_value' in result:
                logger.info(f"Último RSI: {result['latest_value']}")
        except Exception as e:
            logger.error(f"❌ apply_indicator: {e}")
            
    except Exception as e:
        logger.error(f"❌ Error importando módulo: {e}")

def main():
    """Función principal."""
    parser = argparse.ArgumentParser(description="Diagnóstico de datos TradingView")
    parser.add_argument("--symbol", default="NASDAQ:TSLA", help="Símbolo a probar")
    parser.add_argument("--interval", default="1D", help="Intervalo de tiempo")
    parser.add_argument("--bars", type=int, default=10, help="Número de barras")
    parser.add_argument("--test-indicators", action="store_true", help="Probar indicadores")
    parser.add_argument("--test-multiple", action="store_true", help="Probar múltiples símbolos")
    parser.add_argument("--test-functions", action="store_true", help="Probar nuestras funciones")
    parser.add_argument("--verbose", action="store_true", help="Modo verbose")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    logger.info("🚀 Iniciando diagnóstico de TradingView...")
    logger.info(f"Fecha/Hora: {datetime.now()}")
    
    # Verificar entorno
    env_ok, pandas_ta_available = setup_environment()
    if not env_ok:
        logger.error("❌ Entorno no configurado correctamente")
        return 1
    
    # Probar conexión
    tv = test_basic_connection()
    if tv is None:
        logger.error("❌ No se pudo establecer conexión")
        return 1
    
    # Probar símbolo específico
    data = test_symbol_data(tv, args.symbol, args.interval, args.bars)
    
    # Pruebas opcionales
    if args.test_multiple:
        test_multiple_symbols(tv)
    
    if args.test_indicators:
        test_indicators(tv, pandas_ta_available)
    
    if args.test_functions:
        test_our_functions()
    
    logger.info("\n✅ Diagnóstico completado")
    return 0

if __name__ == "__main__":
    sys.exit(main())

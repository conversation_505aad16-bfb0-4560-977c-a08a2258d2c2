#!/usr/bin/env python3
"""
Test rápido de IA.
"""

import sys
import asyncio
sys.path.append('app')

async def test_ai():
    try:
        from app.services.vertex_ai import generate_chat_response, initialize_gemini_model
        from app.tools.tradingview_provider import get_available_tools
        
        print('🧪 Iniciando test...')
        
        tools = get_available_tools()
        model = initialize_gemini_model(tools)
        
        if model:
            print('✅ Modelo OK')
            messages = [{'role': 'user', 'content': 'Hola'}]
            response = await generate_chat_response(messages, model)
            print(f'✅ Respuesta: {response}')
            return True
        else:
            print('❌ Modelo falló')
            return False
            
    except Exception as e:
        print(f'❌ Error: {e}')
        return False

if __name__ == "__main__":
    result = asyncio.run(test_ai())
    print(f'Resultado: {"ÉXITO" if result else "FALLO"}')

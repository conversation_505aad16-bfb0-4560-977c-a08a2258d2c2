#!/usr/bin/env python3
"""
Debug paso a paso para identificar dónde falla
"""

import os
import sys
from pathlib import Path

def load_env():
    """Cargar .env"""
    env_file = Path('.env')
    if env_file.exists():
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()
        print("✅ Variables .env cargadas")
    else:
        print("❌ .env no encontrado")

def test_basic_imports():
    """Probar imports básicos"""
    print("🔍 Probando imports básicos...")
    
    try:
        import asyncio
        print("✅ asyncio")
    except Exception as e:
        print(f"❌ asyncio: {e}")
        return False
    
    try:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, current_dir)
        print("✅ path configurado")
    except Exception as e:
        print(f"❌ path: {e}")
        return False
    
    return True

def test_app_imports():
    """Probar imports de la app"""
    print("🔍 Probando imports de la app...")
    
    try:
        from app.config import settings
        print("✅ app.config")
    except Exception as e:
        print(f"❌ app.config: {e}")
        return False
    
    try:
        from app.tools.tradingview_provider import get_available_tools
        print("✅ app.tools.tradingview_provider")
    except Exception as e:
        print(f"❌ app.tools.tradingview_provider: {e}")
        return False
    
    try:
        from app.services.vertex_ai import initialize_gemini_model
        print("✅ app.services.vertex_ai")
    except Exception as e:
        print(f"❌ app.services.vertex_ai: {e}")
        return False
    
    return True

def test_tools():
    """Probar carga de herramientas"""
    print("🔍 Probando carga de herramientas...")
    
    try:
        from app.tools.tradingview_provider import get_available_tools
        tools = get_available_tools()
        print(f"✅ {len(tools)} herramientas cargadas")
        for i, tool in enumerate(tools):
            if hasattr(tool, '__name__'):
                print(f"  - {tool.__name__}")
            elif isinstance(tool, dict):
                print(f"  - Tool {i+1}: {type(tool)} - {list(tool.keys()) if tool else 'empty'}")
            else:
                print(f"  - Tool {i+1}: {type(tool)}")
        return tools
    except Exception as e:
        print(f"❌ Error cargando herramientas: {e}")
        import traceback
        print(traceback.format_exc())
        return None

def test_model_init(tools):
    """Probar inicialización del modelo"""
    print("🔍 Probando inicialización del modelo...")
    
    try:
        from app.services.vertex_ai import initialize_gemini_model
        print("⏳ Inicializando modelo...")
        model = initialize_gemini_model(tools)
        
        if model is None:
            print("❌ Modelo es None")
            return None
        
        print("✅ Modelo inicializado")
        print(f"Tipo: {type(model)}")
        return model
        
    except Exception as e:
        print(f"❌ Error inicializando modelo: {e}")
        import traceback
        print(traceback.format_exc())
        return None

async def test_simple_call(model):
    """Probar llamada simple"""
    print("🔍 Probando llamada simple...")
    
    try:
        from app.services.vertex_ai import generate_chat_response
        
        messages = [{"role": "user", "content": "Hola"}]
        print("⏳ Enviando mensaje simple...")
        
        result = await generate_chat_response(messages, model)
        print(f"✅ Respuesta recibida: {result.get('type')}")
        
        if result.get('type') == 'text':
            content = result.get('content', '')
            print(f"Contenido: {content[:100]}...")
        
        return result
        
    except Exception as e:
        print(f"❌ Error en llamada simple: {e}")
        import traceback
        print(traceback.format_exc())
        return None

async def test_financial_call(model):
    """Probar llamada financiera"""
    print("🔍 Probando llamada financiera...")
    
    try:
        from app.services.vertex_ai import generate_chat_response
        
        messages = [{"role": "user", "content": "¿Cuál es el precio de Tesla?"}]
        print("⏳ Enviando consulta financiera...")
        
        result = await generate_chat_response(messages, model)
        print(f"✅ Respuesta recibida: {result.get('type')}")
        
        if result.get('type') == 'function_call':
            print(f"🎯 ¡FUNCIÓN DETECTADA!")
            print(f"Función: {result.get('function_name')}")
            print(f"Args: {result.get('function_args')}")
        elif result.get('type') == 'text':
            content = result.get('content', '')
            print(f"Texto: {content[:100]}...")
            if "Lo siento" in content:
                print("❌ FALLBACK DETECTADO")
        
        return result
        
    except Exception as e:
        print(f"❌ Error en llamada financiera: {e}")
        import traceback
        print(traceback.format_exc())
        return None

async def main():
    """Función principal"""
    print("🚀 === DEBUG PASO A PASO ===\n")
    
    # 1. Cargar .env
    load_env()
    
    # 2. Imports básicos
    if not test_basic_imports():
        return
    
    # 3. Imports de la app
    if not test_app_imports():
        return
    
    # 4. Cargar herramientas
    tools = test_tools()
    if tools is None:
        return
    
    # 5. Inicializar modelo
    model = test_model_init(tools)
    if model is None:
        return
    
    # 6. Llamada simple
    simple_result = await test_simple_call(model)
    
    # 7. Llamada financiera
    financial_result = await test_financial_call(model)
    
    # 8. Resumen
    print("\n🏁 === RESUMEN ===")
    print(f"Llamada simple: {'✅' if simple_result else '❌'}")
    print(f"Llamada financiera: {'✅' if financial_result else '❌'}")
    
    if financial_result and financial_result.get('type') == 'function_call':
        print("🎉 ¡PROBLEMA RESUELTO!")
    else:
        print("⚠️ Problema persiste")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
